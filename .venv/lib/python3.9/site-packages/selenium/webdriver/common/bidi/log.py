# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from dataclasses import dataclass


class LogEntryAdded:
    event_class = "log.entryAdded"

    @classmethod
    def from_json(cls, json):
        if json["type"] == "console":
            return ConsoleLogEntry.from_json(json)
        elif json["type"] == "javascript":
            return JavaScriptLogEntry.from_json(json)


@dataclass
class ConsoleLogEntry:
    level: str
    text: str
    timestamp: str
    method: str
    args: list[dict]
    type_: str

    @classmethod
    def from_json(cls, json):
        return cls(
            level=json["level"],
            text=json["text"],
            timestamp=json["timestamp"],
            method=json["method"],
            args=json["args"],
            type_=json["type"],
        )


@dataclass
class JavaScriptLogEntry:
    level: str
    text: str
    timestamp: str
    stacktrace: dict
    type_: str

    @classmethod
    def from_json(cls, json):
        return cls(
            level=json["level"],
            text=json["text"],
            timestamp=json["timestamp"],
            stacktrace=json["stackTrace"],
            type_=json["type"],
        )


class LogLevel:
    """Represents log level."""

    DEBUG = "debug"
    INFO = "info"
    WARN = "warn"
    ERROR = "error"
